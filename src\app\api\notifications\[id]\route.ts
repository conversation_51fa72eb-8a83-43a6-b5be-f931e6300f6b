import { NextRequest, NextResponse } from 'next/server';
import { NotificationModel } from '@/lib/models/notification';

// GET /api/notifications/[id] - Get a specific notification
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const notification = await NotificationModel.findById(id);

    if (!notification) {
      return NextResponse.json(
        { success: false, error: 'Notification not found' },
        { status: 404 }
      );
    }

    // Parse pdfData if it exists and is a string
    let parsedPdfData = null;
    if (notification.pdfData) {
      try {
        parsedPdfData = typeof notification.pdfData === 'string'
          ? JSON.parse(notification.pdfData)
          : notification.pdfData;
      } catch (parseError) {
        console.error('Error parsing pdfData:', parseError);
        parsedPdfData = null;
      }
    }

    // Transform the notification to include pdfUrl and parsed pdfData
    const transformedNotification = {
      ...notification,
      pdfData: parsedPdfData,
      pdfUrl: notification.pdfData ? `/api/notifications/${id}/pdf` : undefined
    };

    return NextResponse.json({
      success: true,
      notification: transformedNotification
    });
  } catch (error) {
    console.error('Error fetching notification:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch notification' },
      { status: 500 }
    );
  }
}

// PATCH /api/notifications/[id] - Update a notification (e.g., mark as read or update content)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { isRead, title, message, pdfFileName, pdfFilePath, pdfData } = body;

    // Handle marking as read
    if (isRead === true) {
      const success = await NotificationModel.markAsRead(id);

      if (!success) {
        return NextResponse.json(
          { success: false, error: 'Notification not found' },
          { status: 404 }
        );
      }

      const updatedNotification = await NotificationModel.findById(id);

      return NextResponse.json({
        success: true,
        notification: updatedNotification
      });
    }

    // Handle full notification update (for edit functionality)
    if (title || message || pdfFileName || pdfFilePath || pdfData) {
      const updateData: any = {};

      if (title) updateData.title = title;
      if (message) updateData.message = message;
      if (pdfFileName) updateData.pdfFileName = pdfFileName;
      if (pdfFilePath) updateData.pdfFilePath = pdfFilePath;
      if (pdfData) updateData.pdfData = pdfData;

      const success = await NotificationModel.update(id, updateData);

      if (!success) {
        return NextResponse.json(
          { success: false, error: 'Notification not found' },
          { status: 404 }
        );
      }

      const updatedNotification = await NotificationModel.findById(id);

      if (!updatedNotification) {
        return NextResponse.json(
          { success: false, error: "Notification not found after update" },
          { status: 404 }
        );
      }

      // Parse pdfData if it exists and is a string
      let parsedPdfData = null;
      if (updatedNotification.pdfData) {
        try {
          parsedPdfData = typeof updatedNotification.pdfData === 'string'
            ? JSON.parse(updatedNotification.pdfData)
            : updatedNotification.pdfData;
        } catch (parseError) {
          console.error('Error parsing pdfData:', parseError);
          parsedPdfData = null;
        }
      }

      // Transform the notification to include pdfUrl and parsed pdfData
      const transformedNotification = {
        ...updatedNotification,
        pdfData: parsedPdfData,
        pdfUrl: updatedNotification.pdfData ? `/api/notifications/${id}/pdf` : undefined
      };

      return NextResponse.json({
        success: true,
        notification: transformedNotification
      });
    }

    return NextResponse.json(
      { success: false, error: 'Invalid update operation' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error updating notification:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update notification' },
      { status: 500 }
    );
  }
}

// DELETE /api/notifications/[id] - Delete a specific notification
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const success = await NotificationModel.delete(id);
    
    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Notification not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      message: 'Notification deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting notification:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete notification' },
      { status: 500 }
    );
  }
}
