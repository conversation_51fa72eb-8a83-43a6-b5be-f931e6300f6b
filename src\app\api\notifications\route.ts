import { NextRequest, NextResponse } from 'next/server';
import { NotificationModel, CreateNotificationData } from '@/lib/models/notification';

// GET /api/notifications - Get all notifications
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = searchParams.get('limit');

    const notifications = await NotificationModel.findAll(
      limit ? parseInt(limit) : undefined
    );
    
    // Transform notifications to include pdfUrl and parse pdfData if it exists
    const transformedNotifications = notifications.map(notification => {
      let parsedPdfData = null;
      if (notification.pdfData) {
        try {
          parsedPdfData = typeof notification.pdfData === 'string'
            ? JSON.parse(notification.pdfData)
            : notification.pdfData;
        } catch (parseError) {
          console.error('Error parsing pdfData for notification:', notification.id, parseError);
          parsedPdfData = null;
        }
      }

      return {
        ...notification,
        pdfData: parsedPdfData,
        pdfUrl: notification.pdfData ? `/api/notifications/${notification.id}/pdf` : undefined
      };
    });

    return NextResponse.json({
      success: true,
      notifications: transformedNotifications
    });
  } catch (error) {
    console.error('Error fetching notifications:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch notifications' },
      { status: 500 }
    );
  }
}

// POST /api/notifications - Create a new notification
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { title, message, type, pdfFileName, pdfData } = body;

    if (!title || !message || !type) {
      return NextResponse.json(
        { success: false, error: 'Title, message, and type are required' },
        { status: 400 }
      );
    }

    const notificationData: CreateNotificationData = {
      title,
      message,
      type,
      pdfFileName,
      pdfData
    };
    
    const notification = await NotificationModel.create(notificationData);
    
    return NextResponse.json({
      success: true,
      notification
    });
  } catch (error) {
    console.error('Error creating notification:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create notification' },
      { status: 500 }
    );
  }
}

// DELETE /api/notifications - Delete all notifications
export async function DELETE(request: NextRequest) {
  try {
    const deletedCount = await NotificationModel.deleteAll();
    
    return NextResponse.json({
      success: true,
      deletedCount
    });
  } catch (error) {
    console.error('Error deleting notifications:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete notifications' },
      { status: 500 }
    );
  }
}
