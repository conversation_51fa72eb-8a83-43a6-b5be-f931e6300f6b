"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Search,
  Filter,
  SortAsc,
  SortDesc,
  Archive,
  Eye,
  RotateCcw,
  Calendar,
  User,
  MapPin,
  FileText,
} from "lucide-react";
import { format } from "date-fns";
import { toast } from "sonner";

interface ArchivedDocument {
  id: string;
  template_name: string;
  applicant_name: string;
  age?: number;
  barangay?: string;
  ctc_number?: string;
  day?: string;
  first_name?: string;
  last_name?: string;
  middle_initial?: string;
  month?: string;
  or_number?: string;
  suffix?: string;
  tin_number?: string;
  year?: string;
  photo_path?: string;
  pdf_path?: string;
  created_at: string;
  approved_at: string;
  approved_by?: string;
  original_notification_id?: string;
  metadata?: string;
}

interface ArchiveFilters {
  templateNames: string[];
}

export default function ArchivesPage() {
  const [documents, setDocuments] = useState<ArchivedDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTemplate, setSelectedTemplate] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("approved_at");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [filters, setFilters] = useState<ArchiveFilters>({
    templateNames: [],
  });
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(20);

  // Fetch filters
  useEffect(() => {
    const fetchFilters = async () => {
      try {
        const response = await fetch("/api/archives?filters=true");
        const data = await response.json();
        setFilters(data);
      } catch (error) {
        console.error("Failed to fetch filters:", error);
      }
    };

    fetchFilters();
  }, []);

  // Fetch documents
  const fetchDocuments = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        search: searchTerm,
        template_name: selectedTemplate === "all" ? "" : selectedTemplate,
        sort_by: sortBy,
        sort_order: sortOrder,
        limit: itemsPerPage.toString(),
        offset: ((currentPage - 1) * itemsPerPage).toString(),
      });

      const response = await fetch(`/api/archives?${params}`);
      const data = await response.json();

      if (data.success) {
        setDocuments(data.data);
        setTotal(data.total);
      }
    } catch (error) {
      console.error("Failed to fetch documents:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDocuments();
  }, [searchTerm, selectedTemplate, sortBy, sortOrder, currentPage]);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(field);
      setSortOrder("desc");
    }
    setCurrentPage(1);
  };

  const clearFilters = () => {
    setSearchTerm("");
    setSelectedTemplate("all");
    setSortBy("approved_at");
    setSortOrder("desc");
    setCurrentPage(1);
  };

  // Handle restore document
  const handleRestore = async (documentId: string) => {
    try {
      const response = await fetch(`/api/archives/${documentId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ action: "restore" }),
      });

      if (response.ok) {
        toast.success("Document restored successfully");
        fetchDocuments(); // Refresh the list
      } else {
        toast.error("Failed to restore document");
      }
    } catch (error) {
      console.error("Error restoring document:", error);
      toast.error("Error restoring document");
    }
  };

  // Handle view document
  const handleView = (documentId: string) => {
    // Navigate to the document view page
    window.open(`/notifications/${documentId}`, "_blank");
  };

  const totalPages = Math.ceil(total / itemsPerPage);

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Archive className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold">Archives</h1>
            <p className="text-muted-foreground">
              Approved documents storage and management
            </p>
          </div>
        </div>
        <Badge variant="secondary" className="text-lg px-3 py-1">
          {total} Documents
        </Badge>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Search & Filter
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search documents..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Template Filter */}
            <Select
              value={selectedTemplate}
              onValueChange={setSelectedTemplate}
            >
              <SelectTrigger>
                <SelectValue placeholder="All Templates" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Templates</SelectItem>
                {filters.templateNames.map((template) => (
                  <SelectItem key={template} value={template}>
                    {template}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Clear Filters */}
            <Button variant="outline" onClick={clearFilters}>
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Documents Table */}
      <Card>
        <CardHeader>
          <CardTitle>Archived Documents</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : documents.length === 0 ? (
            <div className="text-center py-8">
              <Archive className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Documents Found</h3>
              <p className="text-muted-foreground">
                No archived documents match your current filters.
              </p>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>
                      <Button
                        variant="ghost"
                        onClick={() => handleSort("applicant_name")}
                        className="h-auto p-0 font-semibold"
                      >
                        <User className="h-4 w-4 mr-2" />
                        Applicant
                        {sortBy === "applicant_name" &&
                          (sortOrder === "asc" ? (
                            <SortAsc className="h-4 w-4 ml-2" />
                          ) : (
                            <SortDesc className="h-4 w-4 ml-2" />
                          ))}
                      </Button>
                    </TableHead>
                    <TableHead>
                      <Button
                        variant="ghost"
                        onClick={() => handleSort("template_name")}
                        className="h-auto p-0 font-semibold"
                      >
                        <FileText className="h-4 w-4 mr-2" />
                        Template
                        {sortBy === "template_name" &&
                          (sortOrder === "asc" ? (
                            <SortAsc className="h-4 w-4 ml-2" />
                          ) : (
                            <SortDesc className="h-4 w-4 ml-2" />
                          ))}
                      </Button>
                    </TableHead>
                    <TableHead>
                      <MapPin className="h-4 w-4 mr-2 inline" />
                      Barangay
                    </TableHead>
                    <TableHead>
                      <Button
                        variant="ghost"
                        onClick={() => handleSort("approved_at")}
                        className="h-auto p-0 font-semibold"
                      >
                        <Calendar className="h-4 w-4 mr-2" />
                        Approved
                        {sortBy === "approved_at" &&
                          (sortOrder === "asc" ? (
                            <SortAsc className="h-4 w-4 ml-2" />
                          ) : (
                            <SortDesc className="h-4 w-4 ml-2" />
                          ))}
                      </Button>
                    </TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {documents.map((doc) => (
                    <TableRow key={doc.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {doc.applicant_name}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {doc.ctc_number && `CTC: ${doc.ctc_number}`}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{doc.template_name}</Badge>
                      </TableCell>
                      <TableCell>{doc.barangay || "N/A"}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {format(new Date(doc.approved_at), "MMM dd, yyyy")}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {format(new Date(doc.approved_at), "hh:mm a")}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleView(doc.id)}
                            title="View Document"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleRestore(doc.id)}
                            title="Restore Document"
                          >
                            <RotateCcw className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    Showing {(currentPage - 1) * itemsPerPage + 1} to{" "}
                    {Math.min(currentPage * itemsPerPage, total)} of {total}{" "}
                    documents
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(currentPage - 1)}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(currentPage + 1)}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
