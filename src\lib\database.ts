// Server-side only imports
let Database: any;
let path: any;
let fs: any;

// Only import on server side
if (typeof window === 'undefined') {
  Database = require('better-sqlite3');
  path = require('path');
  fs = require('fs').promises;
}

// Database file path
const DB_PATH = typeof window === 'undefined' ? path?.join(process.cwd(), 'data', 'ldis.db') : '';

let db: any = null;

// Ensure data directory exists
const ensureDataDir = async (): Promise<void> => {
  const dataDir = path.dirname(DB_PATH);
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
};

// Initialize database connection
export const initDatabase = async (): Promise<any> => {
  // Only run on server side
  if (typeof window !== 'undefined') {
    throw new Error('Database operations can only be performed on the server side');
  }

  if (db) {
    return db;
  }

  await ensureDataDir();

  db = new Database(DB_PATH);

  // Enable WAL mode for better performance
  db.pragma('journal_mode = WAL');

  // Create tables if they don't exist
  await createTables();

  return db;
};

// Get database instance
export const getDatabase = async (): Promise<any> => {
  if (typeof window !== 'undefined') {
    throw new Error('Database operations can only be performed on the server side');
  }

  if (!db) {
    return await initDatabase();
  }
  return db;
};

// Create database tables
const createTables = async (): Promise<void> => {
  if (!db) return;

  // Users table
  db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT UNIQUE NOT NULL,
      password_hash TEXT NOT NULL,
      recovery_options TEXT, -- JSON string
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Templates table
  db.exec(`
    CREATE TABLE IF NOT EXISTS templates (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT,
      filename TEXT NOT NULL,
      placeholders TEXT, -- JSON array
      layout_size TEXT DEFAULT 'A4',
      has_applicant_photo BOOLEAN DEFAULT FALSE,
      uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Documents table (for generated documents)
  db.exec(`
    CREATE TABLE IF NOT EXISTS documents (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      template_id TEXT NOT NULL,
      document_data TEXT, -- JSON string of form data
      generated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (template_id) REFERENCES templates (id)
    )
  `);

  // Check if notifications table needs migration (remove old columns)
  const tableInfo = db.prepare("PRAGMA table_info(notifications)").all() as any[];
  const hasOldColumns = tableInfo.some((col: any) => col.name === 'user_id' || col.name === 'pdf_file_path');

  if (hasOldColumns) {
    console.log('Migrating notifications table to remove unused columns...');

    // Backup existing data
    const existingData = db.prepare('SELECT id, title, message, type, is_read, created_at, updated_at, pdf_filename, pdf_data FROM notifications').all();

    // Drop old table
    db.exec('DROP TABLE IF EXISTS notifications');

    // Create new table with clean schema
    db.exec(`
      CREATE TABLE notifications (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('info', 'success', 'warning', 'error')),
        is_read BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        pdf_filename TEXT,
        pdf_data TEXT, -- JSON string of embedded PDF data
        admin_id TEXT -- Track which admin performed actions
      )
    `);

    // Restore data
    if (existingData.length > 0) {
      const insertStmt = db.prepare(`
        INSERT INTO notifications (id, title, message, type, is_read, created_at, updated_at, pdf_filename, pdf_data, admin_id)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      for (const row of existingData) {
        insertStmt.run(
          row.id,
          row.title,
          row.message,
          row.type,
          row.is_read,
          row.created_at,
          row.updated_at,
          row.pdf_filename,
          row.pdf_data,
          null // admin_id will be null for existing records
        );
      }
      console.log(`Migrated ${existingData.length} notification records`);
    }
  } else {
    // Create table with new schema if it doesn't exist
    db.exec(`
      CREATE TABLE IF NOT EXISTS notifications (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('info', 'success', 'warning', 'error')),
        is_read BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        pdf_filename TEXT,
        pdf_data TEXT, -- JSON string of embedded PDF data
        admin_id TEXT -- Track which admin performed actions
      )
    `);
  }

  // Archives table for approved documents
  db.exec(`
    CREATE TABLE IF NOT EXISTS archives (
      id TEXT PRIMARY KEY,
      template_name TEXT NOT NULL,
      applicant_name TEXT NOT NULL,
      age INTEGER,
      barangay TEXT,
      ctc_number TEXT,
      day TEXT,
      first_name TEXT,
      last_name TEXT,
      middle_initial TEXT,
      month TEXT,
      or_number TEXT,
      suffix TEXT,
      tin_number TEXT,
      year TEXT,
      photo_path TEXT,
      pdf_path TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      approved_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      approved_by TEXT,
      original_notification_id TEXT,
      metadata TEXT -- JSON string of additional metadata
    )
  `);

  // Create indexes for better performance
  db.exec(`
    CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
    CREATE INDEX IF NOT EXISTS idx_templates_name ON templates(name);
    CREATE INDEX IF NOT EXISTS idx_documents_template_id ON documents(template_id);
    CREATE INDEX IF NOT EXISTS idx_documents_generated_at ON documents(generated_at);
    CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);
    CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);
    CREATE INDEX IF NOT EXISTS idx_notifications_status ON notifications(status);
    CREATE INDEX IF NOT EXISTS idx_archives_applicant_name ON archives(applicant_name);
    CREATE INDEX IF NOT EXISTS idx_archives_template_name ON archives(template_name);
    CREATE INDEX IF NOT EXISTS idx_archives_approved_at ON archives(approved_at);
    CREATE INDEX IF NOT EXISTS idx_archives_barangay ON archives(barangay);
    CREATE INDEX IF NOT EXISTS idx_archives_ctc_number ON archives(ctc_number);
  `);
};

// Close database connection
export const closeDatabase = (): void => {
  if (db) {
    db.close();
    db = null;
  }
};

// Utility function to run migrations
export const runMigrations = async (): Promise<void> => {
  const database = await getDatabase();
  
  // Create migrations table if it doesn't exist
  database.exec(`
    CREATE TABLE IF NOT EXISTS migrations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT UNIQUE NOT NULL,
      executed_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Add any future migrations here
  const migrations: Array<{name: string; sql: string}> = [
    {
      name: '001_add_notification_status',
      sql: 'ALTER TABLE notifications ADD COLUMN status TEXT DEFAULT "active" CHECK (status IN ("active", "dismissed"))'
    }
  ];

  for (const migration of migrations) {
    const existing = database.prepare('SELECT name FROM migrations WHERE name = ?').get(migration.name);

    if (!existing) {
      database.exec(migration.sql);
      database.prepare('INSERT INTO migrations (name) VALUES (?)').run(migration.name);
      console.log(`Migration ${migration.name} executed successfully`);
    }
  }
};

// Archive-related interfaces and functions
export interface ArchivedDocument {
  id: string;
  template_name: string;
  applicant_name: string;
  age?: number;
  barangay?: string;
  ctc_number?: string;
  day?: string;
  first_name?: string;
  last_name?: string;
  middle_initial?: string;
  month?: string;
  or_number?: string;
  suffix?: string;
  tin_number?: string;
  year?: string;
  photo_path?: string;
  pdf_path?: string;
  created_at: string;
  approved_at: string;
  approved_by?: string;
  original_notification_id?: string;
  metadata?: string;
}

export interface ArchiveSearchParams {
  search?: string;
  template_name?: string;
  barangay?: string;
  sort_by?: 'approved_at' | 'applicant_name' | 'template_name';
  sort_order?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

// Archive a document from notification
export const archiveDocument = async (notificationData: any, approvedBy?: string): Promise<ArchivedDocument> => {
  const database = await getDatabase();

  const stmt = database.prepare(`
    INSERT INTO archives (
      id, template_name, applicant_name, age, barangay, ctc_number,
      day, first_name, last_name, middle_initial, month, or_number,
      suffix, tin_number, year, photo_path, pdf_path, approved_by,
      original_notification_id, metadata
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);

  const archivedDoc: ArchivedDocument = {
    id: notificationData.id,
    template_name: notificationData.templateName || '',
    applicant_name: `${notificationData.firstName || ''} ${notificationData.middleInitial || ''} ${notificationData.lastName || ''}`.trim(),
    age: notificationData.age ? parseInt(notificationData.age) : undefined,
    barangay: notificationData.barangay,
    ctc_number: notificationData.ctcNumber,
    day: notificationData.day,
    first_name: notificationData.firstName,
    last_name: notificationData.lastName,
    middle_initial: notificationData.middleInitial,
    month: notificationData.month,
    or_number: notificationData.orNumber,
    suffix: notificationData.suffix,
    tin_number: notificationData.tinNumber,
    year: notificationData.year,
    photo_path: notificationData.photoPath,
    pdf_path: notificationData.pdfPath,
    created_at: notificationData.createdAt || new Date().toISOString(),
    approved_at: new Date().toISOString(),
    approved_by: approvedBy,
    original_notification_id: notificationData.id,
    metadata: JSON.stringify(notificationData)
  };

  stmt.run(
    archivedDoc.id,
    archivedDoc.template_name,
    archivedDoc.applicant_name,
    archivedDoc.age,
    archivedDoc.barangay,
    archivedDoc.ctc_number,
    archivedDoc.day,
    archivedDoc.first_name,
    archivedDoc.last_name,
    archivedDoc.middle_initial,
    archivedDoc.month,
    archivedDoc.or_number,
    archivedDoc.suffix,
    archivedDoc.tin_number,
    archivedDoc.year,
    archivedDoc.photo_path,
    archivedDoc.pdf_path,
    archivedDoc.approved_by,
    archivedDoc.original_notification_id,
    archivedDoc.metadata
  );

  return archivedDoc;
};

// Search archived documents
export const searchArchives = async (params: ArchiveSearchParams = {}): Promise<{
  documents: ArchivedDocument[];
  total: number;
}> => {
  const database = await getDatabase();

  const {
    search = '',
    template_name,
    barangay,
    sort_by = 'approved_at',
    sort_order = 'desc',
    limit = 50,
    offset = 0
  } = params;

  let whereClause = 'WHERE 1=1';
  const queryParams: any[] = [];

  // Search in applicant name, template name, or barangay
  if (search) {
    whereClause += ` AND (
      applicant_name LIKE ? OR
      template_name LIKE ? OR
      barangay LIKE ? OR
      ctc_number LIKE ? OR
      or_number LIKE ?
    )`;
    const searchTerm = `%${search}%`;
    queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm, searchTerm);
  }

  if (template_name) {
    whereClause += ' AND template_name = ?';
    queryParams.push(template_name);
  }

  if (barangay) {
    whereClause += ' AND barangay = ?';
    queryParams.push(barangay);
  }

  const orderClause = `ORDER BY ${sort_by} ${sort_order.toUpperCase()}`;

  // Get total count
  const countStmt = database.prepare(`SELECT COUNT(*) as count FROM archives ${whereClause}`);
  const { count: total } = countStmt.get(...queryParams) as { count: number };

  // Get documents
  const stmt = database.prepare(`
    SELECT * FROM archives
    ${whereClause}
    ${orderClause}
    LIMIT ? OFFSET ?
  `);

  const documents = stmt.all(...queryParams, limit, offset) as ArchivedDocument[];

  return { documents, total };
};

// Get unique values for filters
export const getArchiveFilters = async () => {
  const database = await getDatabase();

  const templateNames = database.prepare('SELECT DISTINCT template_name FROM archives WHERE template_name IS NOT NULL ORDER BY template_name').all() as { template_name: string }[];

  return {
    templateNames: templateNames.map(t => t.template_name)
  };
};

// Get archived document by ID
export const getArchivedDocument = async (id: string): Promise<ArchivedDocument | null> => {
  const database = await getDatabase();
  const stmt = database.prepare('SELECT * FROM archives WHERE id = ?');
  return stmt.get(id) as ArchivedDocument | null;
};

// Delete archived document
export const deleteArchivedDocument = async (id: string): Promise<boolean> => {
  const database = await getDatabase();
  const stmt = database.prepare('DELETE FROM archives WHERE id = ?');
  const result = stmt.run(id);
  return result.changes > 0;
};

// Restore archived document back to notifications
export const restoreArchivedDocument = async (id: string): Promise<boolean> => {
  const database = await getDatabase();

  // Get the archived document
  const archivedDoc = await getArchivedDocument(id);
  if (!archivedDoc) {
    return false;
  }

  try {
    // Parse the metadata to reconstruct the original notification data
    let originalData: any = {};
    if (archivedDoc.metadata) {
      try {
        originalData = JSON.parse(archivedDoc.metadata);
      } catch (error) {
        console.error('Error parsing archived document metadata:', error);
      }
    }

    // Create a new notification from the archived document
    const notificationStmt = database.prepare(`
      INSERT INTO notifications (
        id, title, message, type, pdf_filename, pdf_data, admin_id, created_at, updated_at, is_read
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const now = new Date().toISOString();
    const title = `${archivedDoc.template_name}`;
    const message = `Document restored for ${archivedDoc.applicant_name}`;

    // Ensure we have a proper filename for the approval process
    const pdfFileName = originalData.originalFileName ||
                       originalData.pdfFileName ||
                       `${archivedDoc.applicant_name}_${archivedDoc.template_name}.pdf`;

    // Ensure the pdfData has the originalFileName field for approval process
    let restoredPdfData = originalData;
    if (typeof restoredPdfData === 'object' && restoredPdfData !== null) {
      restoredPdfData = {
        ...restoredPdfData,
        originalFileName: pdfFileName,
        pdfFileName: pdfFileName,
        restoredAt: now,
        restoredFrom: 'archives'
      };
    }

    notificationStmt.run(
      archivedDoc.id, // Keep the same ID
      title,
      message,
      'info',
      pdfFileName, // Ensure we have a filename
      JSON.stringify(restoredPdfData), // Ensure pdfData is properly formatted
      'system', // Mark as system-generated restoration
      now,
      now,
      0 // Mark as unread
    );

    // Remove from archives
    const deleteStmt = database.prepare('DELETE FROM archives WHERE id = ?');
    deleteStmt.run(id);

    return true;
  } catch (error) {
    console.error('Error restoring archived document:', error);
    return false;
  }
};
