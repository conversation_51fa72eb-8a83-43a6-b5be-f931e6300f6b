"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { AlertCircle, RefreshCw, ZoomIn, ZoomOut } from "lucide-react";
import type { PDFData } from "@/hooks/useNotifications";
import html2canvas from "html2canvas";

interface DocumentPreviewProps {
  pdfData: PDFData;
  className?: string;
}

export function DocumentPreview({
  pdfData,
  className = "",
}: DocumentPreviewProps) {
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [zoomLevel, setZoomLevel] = useState(1);

  const generatePreview = async () => {
    if (!pdfData.templateId) {
      setError("No template ID available");
      return;
    }

    setIsGenerating(true);
    setError(null);

    // Debug: Log the pdfData to see what we have
    console.log("PDF Data for preview:", {
      templateId: pdfData.templateId,
      templateName: pdfData.templateName,
      hasPhoto: !!pdfData.photoBase64,
      photoLength: pdfData.photoBase64?.length,
      userData: pdfData.userData,
      status: pdfData.status,
    });

    try {
      // Generate the document HTML using the template API
      const response = await fetch("/api/templates/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          templateId: pdfData.templateId,
          data: pdfData.userData,
          photoPath: null,
          photoBase64: pdfData.photoBase64, // Pass the base64 photo data
          addQRCode: pdfData.status === "approved", // Add QR code if approved
          qrCodeText: pdfData.status === "approved" ? "Validated" : undefined,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to generate document preview");
      }

      const result = await response.json();

      // Create a temporary iframe to render the HTML
      const iframe = document.createElement("iframe");
      iframe.style.position = "absolute";
      iframe.style.left = "-9999px";
      iframe.style.width = "794px"; // A4 width in pixels at 96 DPI
      iframe.style.height = "1123px"; // A4 height in pixels at 96 DPI
      document.body.appendChild(iframe);

      const iframeDoc =
        iframe.contentDocument || iframe.contentWindow?.document;
      if (!iframeDoc) {
        throw new Error("Could not access iframe document");
      }

      iframeDoc.open();
      iframeDoc.write(result.htmlContent);
      iframeDoc.close();

      // Handle photo replacement if needed
      if (pdfData.photoBase64) {
        // Wait a bit for the iframe content to fully load
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Try multiple selectors to find the photo placeholder
        const photoSelectors = [
          'img[src="APPLICANT_PHOTO_PLACEHOLDER"]', // Our specific placeholder
          'img[alt="applicants_photo"]', // Specific to good-moral-certificate template
          'img[alt="Applicant Photo"]',
          'img[alt="applicant photo"]',
          'img[src*="image003.png"]', // Specific image file from good-moral-certificate
          'img[src*="placeholder"]',
          'img[src*="photo"]',
          ".applicant-photo img",
          ".photo img",
          'img[class*="photo"]',
        ];

        let photoFound = false;
        for (const selector of photoSelectors) {
          const photoElements = iframeDoc.querySelectorAll(selector);
          if (photoElements.length > 0) {
            photoElements.forEach((img) => {
              const imgElement = img as HTMLImageElement;
              imgElement.src = pdfData.photoBase64!;

              // Preserve original dimensions if they exist, otherwise use default
              const originalWidth =
                imgElement.width || imgElement.getAttribute("width");
              const originalHeight =
                imgElement.height || imgElement.getAttribute("height");

              if (originalWidth && originalHeight) {
                imgElement.style.width = `${originalWidth}px`;
                imgElement.style.height = `${originalHeight}px`;
              } else {
                // Default dimensions for good-moral-certificate template
                imgElement.style.width = "192px";
                imgElement.style.height = "192px";
              }

              imgElement.style.objectFit = "cover";
              imgElement.style.objectPosition = "center";
              console.log(
                "Photo replaced using selector:",
                selector,
                "with dimensions:",
                imgElement.style.width,
                "x",
                imgElement.style.height
              );
            });
            photoFound = true;
            break;
          }
        }

        if (!photoFound) {
          console.warn(
            "No photo placeholder found in document. Available images:",
            Array.from(iframeDoc.querySelectorAll("img")).map((img) => ({
              src: (img as HTMLImageElement).src,
              alt: (img as HTMLImageElement).alt,
              className: (img as HTMLImageElement).className,
            }))
          );
        }
      }

      // Wait for content and images to load
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Convert to canvas
      const canvas = await html2canvas(iframeDoc.body, {
        width: 794,
        height: 1123,
        scale: 2, // Higher resolution
        useCORS: true,
        allowTaint: true,
        backgroundColor: "#ffffff",
      });

      // Convert canvas to image
      const imageDataUrl = canvas.toDataURL("image/jpeg", 0.9);
      setPreviewImage(imageDataUrl);

      // Clean up
      document.body.removeChild(iframe);
    } catch (error) {
      console.error("Error generating preview:", error);
      setError("Failed to generate document preview");
    } finally {
      setIsGenerating(false);
    }
  };

  const zoomIn = () => {
    setZoomLevel((prev) => Math.min(prev + 0.25, 3)); // Max zoom 3x
  };

  const zoomOut = () => {
    setZoomLevel((prev) => Math.max(prev - 0.25, 0.5)); // Min zoom 0.5x
  };

  const resetZoom = () => {
    setZoomLevel(1);
  };

  useEffect(() => {
    generatePreview();
  }, [pdfData]);

  // Keyboard shortcuts for zoom
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case "=":
          case "+":
            e.preventDefault();
            zoomIn();
            break;
          case "-":
            e.preventDefault();
            zoomOut();
            break;
          case "0":
            e.preventDefault();
            resetZoom();
            break;
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, []);

  return (
    <div className={`flex flex-col ${className}`}>
      {/* Header with controls */}
      <div className="flex items-center justify-between p-4 border-b bg-background">
        <h3 className="text-lg font-semibold">Document Preview</h3>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={generatePreview}
            disabled={isGenerating}
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${isGenerating ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
          {previewImage && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={zoomOut}
                disabled={zoomLevel <= 0.5}
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={resetZoom}
                disabled={zoomLevel === 1}
              >
                {Math.round(zoomLevel * 100)}%
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={zoomIn}
                disabled={zoomLevel >= 3}
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Preview content */}
      <div className="flex-1 overflow-hidden bg-gray-50 p-4">
        {isGenerating ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
              <p className="text-muted-foreground">Generating preview...</p>
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Preview Error</h3>
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={generatePreview} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </div>
          </div>
        ) : previewImage ? (
          <div
            className="h-full w-full overflow-auto"
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "flex-start",
              minHeight: "100%",
              paddingTop: "20px",
              paddingBottom: "20px",
            }}
          >
            <div
              className="bg-white shadow-lg rounded-lg overflow-hidden"
              style={{
                transform: `scale(${zoomLevel})`,
                transformOrigin: "top center",
                transition: "transform 0.2s ease-in-out",
                width: "794px", // A4 width at 96 DPI
                marginLeft: "20px",
                marginRight: "20px",
                marginTop: zoomLevel > 1 ? "20px" : "0px",
                marginBottom: zoomLevel > 1 ? "20px" : "0px",
                flexShrink: 0,
              }}
            >
              <img
                src={previewImage}
                alt="Document Preview"
                className="w-full h-auto block"
                style={{
                  width: "794px", // Fixed A4 width
                  height: "auto",
                  display: "block",
                }}
              />
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                No Preview Available
              </h3>
              <p className="text-muted-foreground">
                Unable to generate document preview.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
