import { NextRequest, NextResponse } from 'next/server';
import { archiveDocument } from '@/lib/database';
import { NotificationModel } from '@/lib/models/notification';
import { getCurrentAdmin } from '@/lib/admin-utils';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Get the current admin from session
    const currentAdmin = await getCurrentAdmin(request);
    const approvedBy = currentAdmin || 'admin';

    // Get notification data from database
    const notification = await NotificationModel.findById(id);

    if (!notification) {
      return NextResponse.json(
        { success: false, error: 'Notification not found' },
        { status: 404 }
      );
    }

    // Parse the stored PDF data
    let notificationData;
    try {
      const pdfDataString = typeof notification.pdfData === 'string'
        ? notification.pdfData
        : JSON.stringify(notification.pdfData || {});
      notificationData = JSON.parse(pdfDataString);
    } catch (parseError) {
      console.error('Error parsing notification PDF data:', parseError);
      return NextResponse.json(
        { success: false, error: 'Invalid notification data format' },
        { status: 400 }
      );
    }

    // Add the notification ID to the data
    notificationData.id = id;
    notificationData.templateName = notificationData.templateName || 'Unknown Template';

    // Archive the document to SQLite
    const archivedDoc = await archiveDocument(notificationData, approvedBy);

    return NextResponse.json({
      success: true,
      message: 'Document approved and archived to database',
      data: archivedDoc
    });

  } catch (error) {
    console.error('Approve notification API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to approve and archive document' },
      { status: 500 }
    );
  }
}
