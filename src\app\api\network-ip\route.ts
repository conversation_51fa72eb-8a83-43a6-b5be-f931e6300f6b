import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Simple fallback to localhost for now
    const host = request.headers.get('host') || 'localhost:3000';
    const baseURL = `http://${host}`;

    // Return the network IP as JSON
    return NextResponse.json({
      baseURL,
      networkIP: host,
      success: true
    });
  } catch (error) {
    console.error('Error getting network IP:', error);

    // Fallback to localhost
    return NextResponse.json({
      baseURL: 'http://localhost:3000',
      networkIP: 'localhost:3000',
      success: false,
      error: 'Could not detect network IP'
    });
  }
}
